package com.wzsec.webproxy.util;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.impl.InvisibleWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 水印打印工具
 * 用于在控制台打印水印前后的文字对比
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
public class WatermarkPrinter {

    private static final InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();

    /**
     * 打印水印前后的文字对比
     */
    public static void printWatermarkComparison(String originalText, String contentType) {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🔒 DWTS 暗水印演示 - 文字对比");
        System.out.println("=".repeat(80));

        try {
            // 创建模拟请求和配置
            MockHttpServletRequest mockRequest = createMockRequest();
            WebProxyConfig mockConfig = createMockConfig();

            // 处理水印
            byte[] watermarkedBytes = processor.processWatermark(
                    originalText.getBytes(StandardCharsets.UTF_8),
                    contentType,
                    mockRequest,
                    mockConfig
            );

            String watermarkedText = new String(watermarkedBytes, StandardCharsets.UTF_8);

            // 打印对比结果
            printComparison(originalText, watermarkedText, contentType);

        } catch (Exception e) {
            System.err.println("❌ 水印处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 打印详细对比信息
     */
    private static void printComparison(String original, String watermarked, String contentType) {
        System.out.println("\n📊 基本信息:");
        System.out.println("   内容类型: " + contentType);
        System.out.println("   原始长度: " + original.length() + " 字符");
        System.out.println("   水印长度: " + watermarked.length() + " 字符");
        System.out.println("   增加字符: " + (watermarked.length() - original.length()) + " 个零宽字符");

        System.out.println("\n📄 原始内容:");
        System.out.println("┌" + "─".repeat(78) + "┐");
        printWithLineNumbers(original);
        System.out.println("└" + "─".repeat(78) + "┘");

        System.out.println("\n🔒 加水印内容:");
        System.out.println("┌" + "─".repeat(78) + "┐");
        printWithLineNumbers(watermarked);
        System.out.println("└" + "─".repeat(78) + "┘");

        // 分析差异
        Map<String, Object> analysis = analyzeWatermarkDifference(original, watermarked);
        printAnalysis(analysis);

        // 显示零宽字符的可视化表示
        printZeroWidthVisualization(watermarked);

        // 显示Unicode表示
        printUnicodeRepresentation(watermarked);
    }

    /**
     * 带行号打印文本
     */
    private static void printWithLineNumbers(String text) {
        String[] lines = text.split("\n");
        for (int i = 0; i < lines.length; i++) {
            System.out.printf("│%3d │ %s%n", i + 1, lines[i]);
        }
        if (lines.length == 1 && !text.contains("\n")) {
            // 单行文本，直接显示
            System.out.printf("│  1 │ %s%n", text);
        }
    }

    /**
     * 打印分析结果
     */
    private static void printAnalysis(Map<String, Object> analysis) {
        System.out.println("\n🔍 差异分析:");
        
        @SuppressWarnings("unchecked")
        Map<Integer, String> insertedPositions = (Map<Integer, String>) analysis.get("insertedPositions");
        
        @SuppressWarnings("unchecked")
        Map<String, Integer> charTypeStats = (Map<String, Integer>) analysis.get("zeroWidthCharTypes");

        System.out.println("   插入字符总数: " + analysis.get("totalInsertedChars"));
        
        if (!insertedPositions.isEmpty()) {
            System.out.println("   插入位置详情:");
            insertedPositions.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> 
                            System.out.println("     位置 " + entry.getKey() + ": " + entry.getValue()));
        }

        if (!charTypeStats.isEmpty()) {
            System.out.println("   零宽字符类型统计:");
            charTypeStats.forEach((charType, count) -> 
                    System.out.println("     " + charType + ": " + count + " 个"));
        }
    }

    /**
     * 打印零宽字符可视化表示
     */
    private static void printZeroWidthVisualization(String text) {
        System.out.println("\n👁️ 零宽字符可视化 (用标签表示):");
        System.out.println("┌" + "─".repeat(78) + "┐");
        
        StringBuilder visualization = new StringBuilder();
        for (char c : text.toCharArray()) {
            String charName = getZeroWidthCharName(c);
            if (charName != null) {
                visualization.append("[").append(charName).append("]");
            } else {
                visualization.append(c);
            }
        }
        
        System.out.printf("│ %s%n", visualization.toString());
        System.out.println("└" + "─".repeat(78) + "┘");
    }

    /**
     * 打印Unicode表示
     */
    private static void printUnicodeRepresentation(String text) {
        System.out.println("\n🔢 Unicode 表示:");
        System.out.println("┌" + "─".repeat(78) + "┐");
        
        StringBuilder unicode = new StringBuilder();
        for (char c : text.toCharArray()) {
            if (getZeroWidthCharName(c) != null || c > 127) {
                unicode.append("\\u").append(String.format("%04X", (int) c));
            } else {
                unicode.append(c);
            }
        }
        
        // 分行显示，避免过长
        String unicodeStr = unicode.toString();
        int lineLength = 76;
        for (int i = 0; i < unicodeStr.length(); i += lineLength) {
            int end = Math.min(i + lineLength, unicodeStr.length());
            System.out.printf("│ %s%n", unicodeStr.substring(i, end));
        }
        
        System.out.println("└" + "─".repeat(78) + "┘");
    }

    /**
     * 分析水印差异
     */
    private static Map<String, Object> analyzeWatermarkDifference(String original, String watermarked) {
        Map<String, Object> analysis = new HashMap<>();
        Map<Integer, String> insertedChars = new HashMap<>();
        Map<String, Integer> charTypeStats = new HashMap<>();

        int originalIndex = 0;
        int watermarkedIndex = 0;

        while (originalIndex < original.length() && watermarkedIndex < watermarked.length()) {
            char originalChar = original.charAt(originalIndex);
            char watermarkedChar = watermarked.charAt(watermarkedIndex);

            if (originalChar == watermarkedChar) {
                originalIndex++;
                watermarkedIndex++;
            } else {
                String charName = getZeroWidthCharName(watermarkedChar);
                if (charName != null) {
                    insertedChars.put(watermarkedIndex, charName);
                    charTypeStats.merge(charName, 1, Integer::sum);
                    watermarkedIndex++;
                } else {
                    originalIndex++;
                    watermarkedIndex++;
                }
            }
        }

        // 处理末尾的插入字符
        while (watermarkedIndex < watermarked.length()) {
            char watermarkedChar = watermarked.charAt(watermarkedIndex);
            String charName = getZeroWidthCharName(watermarkedChar);
            if (charName != null) {
                insertedChars.put(watermarkedIndex, charName);
                charTypeStats.merge(charName, 1, Integer::sum);
            }
            watermarkedIndex++;
        }

        analysis.put("insertedPositions", insertedChars);
        analysis.put("totalInsertedChars", insertedChars.size());
        analysis.put("zeroWidthCharTypes", charTypeStats);

        return analysis;
    }

    /**
     * 获取零宽字符名称
     */
    private static String getZeroWidthCharName(char c) {
        switch (c) {
            case '\u200B': return "ZERO_WIDTH_SPACE";
            case '\u200C': return "ZERO_WIDTH_NON_JOINER";
            case '\u200D': return "ZERO_WIDTH_JOINER";
            case '\u2060': return "WORD_JOINER";
            case '\u2062': return "INVISIBLE_SEPARATOR";
            default: return null;
        }
    }

    /**
     * 创建模拟HTTP请求
     */
    private static MockHttpServletRequest createMockRequest() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("192.168.1.100");
        request.setRequestURI("/api/demo");
        request.getSession().setAttribute("username", "demo_user");
        return request;
    }

    /**
     * 创建模拟配置
     */
    private static WebProxyConfig createMockConfig() {
        WebProxyConfig config = new WebProxyConfig();
        config.setProxyName("demo-proxy");
        config.setEnableInvisibleWatermark(true);
        config.setInvisibleEncodingStrength("medium");
        config.setInvisibleEmbedDensity(0.3);
        config.setWatermarkText("DWTS演示水印");
        return config;
    }

    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        // 测试JSON内容
        String jsonContent = "{\"users\":[{\"id\":1,\"name\":\"张三\",\"email\":\"<EMAIL>\"},{\"id\":2,\"name\":\"李四\",\"email\":\"<EMAIL>\"}]}";
        printWatermarkComparison(jsonContent, "application/json");

        System.out.println("\n" + "=".repeat(80));
        
        // 测试纯文本内容
        String textContent = "这是一段测试文本，用于演示暗水印技术的效果。";
        printWatermarkComparison(textContent, "text/plain");
    }
}
